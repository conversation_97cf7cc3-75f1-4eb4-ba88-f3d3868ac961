<?php
session_start();
$_SESSION["educhat"]["loggedInUser"]["accounttype"] = "WHITELIST_STAFF";
$_SESSION["educhat"]['conversation']['history'] = [];
$selLang = $_SESSION["educhat"]['conversation']['lang'] ?? "zh-HK";
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<title>GenAI Take Notes</title>
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- jQuery UI CSS -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<link rel="stylesheet" href="./styles.css?v=<?php echo time();?>" type="text/css">

<!-- Bootstrap Icons -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.5.0/font/bootstrap-icons.min.css" rel="stylesheet">
</head>
<body>
<header id="overAllHeader" class="d-flex fixed-top p-2">
    <span class="d-flex ms-auto me-auto">
        <img src="../assets/images/EdUHK_Logo_RGB.png" alt="" style="vertical-align: sub;max-height: 47px;">
        <span class="h3" style="padding-top: 8px;">GenAI <small class="fst-italic me-2">Take Notes</small><sup class="text-danger" style="font-style:italic;display:none;">DEV</sup> - RGC Forum 2025</span>
    </span>
</header>
<!-- Button Group Container -->
<div id="buttonGroup" class="button-group">
    <button class="record-button btn btn-dark rounded-circle" id="recordButton" title="Start/Stop Recording">
        <i class="bi bi-mic-fill"></i>
    </button>
    <button id="settingsButton" class="settings-button" title="Settings">
        <i class="bi bi-gear-fill"></i>
    </button>
    <button id="toggleHeaderBtn" class="toggle-header-btn" title="Toggle header visibility">
        <i class="bi bi-chevron-compact-up"></i>
    </button>
</div>
<div id="menuTabHeader" class="fixed-top d-flex" style="margin-top: 68px; display: none;">
    <ul class="nav nav-tabs ms-auto me-auto" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="true">Notes</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab" aria-controls="summary" aria-selected="false">Summarize</button>
        </li>
    </ul>
</div>
<div class="tab-content" id="myTabContent">
    <div class="tab-pane fade show active" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
        <!-- Split screen layout for notes and auto-summary -->
        <div class="split-screen-container">

            <!-- Left side - Transcription -->
            <div class="split-screen-left">
                <div id="audioContainer" class="container text-center">
                    <div id="speakingAnimation" class="mt-3" style="display:none;">
                        <div class="synthesis-bar"></div>
                        <div class="synthesis-bar"></div>
                        <div class="synthesis-bar"></div>
                        <div class="synthesis-bar"></div>
                        <div class="synthesis-bar"></div>
                    </div>
                    <div class="content-header">
                        <span>Transcription</span>
                        <button class="collapse-expand-btn collapse-transcription" id="collapseTranscriptionBtn" title="Hide Transcription">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button class="collapse-expand-btn show-both-btn" id="showBothFromTranscription" title="Show Both Panels" style="display:none;">
                            <i class="bi bi-layout-split"></i>
                        </button>
                        <div class="content-actions">
                            <div class="font-size-controls me-2">
                                <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="decreaseTranscriptFont" title="Decrease font size">
                                    <i class="bi bi-dash-lg"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="increaseTranscriptFont" title="Increase font size">
                                    <i class="bi bi-plus-lg"></i>
                                </button>
                            </div>
                            <button class="btn btn-sm btn-outline-light action-btn" id="copyTranscriptBtn" title="Copy to clipboard">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-light action-btn" id="exportTranscriptBtn" title="Export to Text">
                                <i class="bi bi-file-earmark-text"></i>
                            </button>
                        </div>
                    </div>
                    <div class="output-text text-start" id="outputText"></div>
                </div>
            </div>
            <!-- Right side - Auto Summary -->
            <div class="split-screen-right">
                <div class="auto-summary-container">
                    <div class="content-header">
                        <span class="auto-summary-header">Auto Summary</span>
                        <div id="autoSummaryThinking" style="display:none;">
                            <div class="spinner"></div>
                        </div>
                        <button class="collapse-expand-btn collapse-summary" id="collapseSummaryBtn" title="Hide Summary">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                        <button class="collapse-expand-btn show-both-btn" id="showBothFromSummary" title="Show Both Panels" style="display:none;">
                            <i class="bi bi-layout-split"></i>
                        </button>
                        <div class="content-actions">
                            <div class="font-size-controls me-2">
                                <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="decreaseSummaryFont" title="Decrease font size">
                                    <i class="bi bi-dash-lg"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="increaseSummaryFont" title="Increase font size">
                                    <i class="bi bi-plus-lg"></i>
                                </button>
                            </div>
                            <button class="btn btn-sm btn-outline-light action-btn" id="copySummaryBtn" title="Copy to clipboard">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-light action-btn" id="exportSummaryBtn" title="Export to Text">
                                <i class="bi bi-file-earmark-text"></i>
                            </button>
                        </div>
                    </div>
                    <div id="autoSummaryText" class="text-start"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-pane fade" id="summary" role="tabpanel" aria-labelledby="summary-tab">
        <!-- Content for the summary tab goes here -->
        <div class="summary-container">
            <div class="thinking-animation" id="thinkingAnimation" style="display:none;">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="summary-controls text-center mb-4">
                <button id="btnGenerateSummary" class="btn btn-primary">Generate Summary</button>
            </div>
            <div class="content-header">
                <span>Full Summary</span>
                <div class="content-actions">
                    <div class="font-size-controls me-2">
                        <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="decreaseFullSummaryFont" title="Decrease font size">
                            <i class="bi bi-dash-lg"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-light action-btn font-size-btn" id="increaseFullSummaryFont" title="Increase font size">
                            <i class="bi bi-plus-lg"></i>
                        </button>
                    </div>
                    <button class="btn btn-sm btn-outline-light action-btn" id="copyFullSummaryBtn" title="Copy to clipboard">
                        <i class="bi bi-clipboard"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-light action-btn" id="exportFullSummaryBtn" title="Export to Text">
                        <i class="bi bi-file-earmark-text"></i>
                    </button>
                </div>
            </div>
            <div id="streamedSummaryText" class="text-start"></div>
        </div>
    </div>
</div>
<button id="btnReGenerateSummary" class="btn btn-primary my-4 ms-auto me-auto" style="display:none"><i class="bi bi-arrow-repeat me-2"></i> Re-Generate</button>



<!-- Settings Modal -->
<div id="settingsModal" class="settings-modal" style="display: none;">
    <div class="settings-modal-content">
        <div class="settings-header">
            <h3>Settings</h3>
            <button class="settings-close" id="closeSettings">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>
        <div class="settings-body">
            <!-- Theme Settings -->
            <div class="settings-section">
                <h4>Theme</h4>
                <div class="theme-options">
                    <label class="theme-option">
                        <input type="radio" name="theme" value="dark" checked>
                        <span class="theme-label">Dark Mode</span>
                    </label>
                    <label class="theme-option">
                        <input type="radio" name="theme" value="light">
                        <span class="theme-label">Light Mode</span>
                    </label>
                </div>
            </div>

            <!-- Auto Summary Settings -->
            <div class="settings-section">
                <h4>Auto Summary Configuration</h4>

                <!-- Model Selection -->
                <div class="setting-group">
                    <label for="modelSelect">AI Model</label>
                    <select id="modelSelect" class="form-control">
                        <option value="gpt-4o" selected>GPT-4o</option>
                        <option value="gpt-4o-mini">GPT-4o-mini</option>
                    </select>
                </div>

                <!-- Temperature -->
                <div class="setting-group">
                    <label for="temperatureSlider">Temperature: <span id="temperatureValue">0.1</span></label>
                    <input type="range" id="temperatureSlider" class="form-range" min="0" max="2" step="0.1" value="0.1">
                </div>

                <!-- Background -->
                <div class="setting-group">
                    <label for="backgroundText">Background</label>
                    <textarea id="backgroundText" class="form-control" rows="3" placeholder="Enter background context for the AI..."></textarea>
                </div>

                <!-- Instructions -->
                <div class="setting-group">
                    <label for="instructionsText">Instructions</label>
                    <textarea id="instructionsText" class="form-control" rows="6" placeholder="Enter instructions for the AI..."></textarea>
                </div>
            </div>
        </div>
        <div class="settings-footer">
            <button id="resetSettings" class="btn btn-secondary">Reset to Default</button>
            <button id="saveSettings" class="btn btn-primary">Save Settings</button>
        </div>
    </div>
</div>


<div id="langToggleButton" class="d-none">Lang</div>
<div class="language-switch" id="languageSwitch">
    <div class="language-button" data-lang="zh-HK">粵</div>
    <div class="language-button" data-lang="zh-CN">普</div>
    <div class="language-button" data-lang="en-US">EN</div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- jQuery UI -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- Azure Speech SDK -->
<script src="https://aka.ms/csspeech/jsbrowserpackageraw"></script>
<script src="../assets/js/marked.min.js"></script>
<script>
    var userSelectedLang = "<?=$selLang?>";
</script>
<script src="./takenotes.js?v=<?php echo time();?>"></script>
</body>
</html>