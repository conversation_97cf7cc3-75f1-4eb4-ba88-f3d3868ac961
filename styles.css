body {
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #212324;
    color: #fff;
    font-family: Arial, sans-serif;
    border-top: 4px solid #f06423;
}

#myTabContent {
    width: 100%;
}

.record-button {
    width: 50px;
    height: 50px;
    font-size: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: fixed;
    bottom: 10px;
    right: 35px;
    box-shadow: 0px 0px 55px rgba(200, 200, 200, 0.7);
    z-index: 1100;
}

.record-button i {
    font-size: 1.5rem;
    color: #fff;
}

.record-button.recording {
    background-color: #dc3545; /* Bootstrap danger color */
    animation: pulse 1.0s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 20px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.split-screen-container {
    display: flex;
    width: 100%;
    height: calc(100vh - 116px);
    margin-top: 116px;
    transition: height 0.3s ease, margin-top 0.3s ease;
    position: relative;
}

.split-screen-left {
    flex: 2;
    border-right: 1px solid #444;
    overflow: hidden;
    position: relative;
    transition: flex 0.3s ease;
}

.split-screen-right {
    flex: 1;
    overflow: hidden;
    position: relative;
    transition: flex 0.3s ease;
}

/* Collapsed states for split screen */
.transcription-collapsed .split-screen-left {
    flex: 0;
    width: 0;
    padding: 0;
    border-right: none;
    overflow: hidden;
}

.transcription-collapsed #audioContainer {
    display: none;
}

.transcription-collapsed .split-screen-right {
    flex: 1;
    width: 100%;
}

.summary-collapsed .split-screen-right {
    flex: 0;
    width: 0;
    padding: 0;
    overflow: hidden;
}

.summary-collapsed .auto-summary-container {
    display: none;
}

.summary-collapsed .split-screen-left {
    flex: 1;
    width: 100%;
    border-right: none;
}

.auto-summary-container {
    padding: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#audioContainer {
    padding: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: none;
}

.content-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
}

.content-header .content-actions {
    margin-left: auto;
}

.collapse-expand-btn {
    margin-left: 10px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.collapse-expand-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.collapse-expand-btn i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

/* Visual indicator for collapsed state */
.collapse-expand-btn.is-collapsed {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.collapse-expand-btn.is-collapsed:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Icon rotation for collapsed state */
.transcription-collapsed .collapse-transcription i,
.summary-collapsed .collapse-summary i {
    transform: rotate(180deg);
}

/* Show both panels button */
.show-both-btn {
    /* background-color: rgba(255, 255, 255, 0.3); */
    border-color: rgba(255, 255, 255, 0.5);
    margin-left: 5px;
}

.show-both-btn:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Show the show-both button when appropriate panel is collapsed */
.transcription-collapsed #showBothFromSummary {
    display: inline-flex !important;
}

.summary-collapsed #showBothFromTranscription {
    display: inline-flex !important;
}

.content-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.font-size-controls {
    display: flex;
    gap: 4px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    padding-right: 8px;
}

.font-size-btn {
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.font-size-btn i {
    font-size: 14px;
}

.action-btn {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
    background-color: var(--accent-color);
    color: var(--primary-dark);
    border-color: var(--accent-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auto-summary-header {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
    display: inline-block;
    vertical-align: middle;
}

#autoSummaryText {
    font-size: 16px;
    overflow-y: auto;
    flex-grow: 1;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 10px;
    color: #fff;
}

#autoSummaryText ul, #autoSummaryText ol {
    padding-left: 20px;
}

#autoSummaryText li {
    margin-bottom: 5px;
}

#autoSummaryThinking {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
    z-index: 10;
}

#outputText {
    font-size: 20px;
    height: calc(100% - 20px);
    overflow-y: auto;
    width: 99%;
    margin-left: auto;
    margin-right: auto;
    flex-grow: 1;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 10px;
}

#outputText .final {
    color: #fff;
}

#outputText .interim {
    color: #cad42e;
}

#speakingAnimation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    margin-top: 20px;
}

#speakingAnimation .synthesis-bar {
    width: 18px;
    height: 24px;
    margin: 0 4px;
    background: #ffffff;
    border-radius: 50%;
}

#speakingAnimation .synthesis-bar.animate {
    animation: synthesis 0.3s infinite;
}

@keyframes synthesis {
    0%, 100% {
        height: 20px;
    }
    50% {
        height: 50px;
    }
}

.thinking-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 20px;
    width: 20px;
}

/* Loading spinner animation for auto summary */
#autoSummaryThinking {
    width: 20px;
    height: 20px;
}

#autoSummaryThinking .spinner {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 2px solid #eeeeee;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Keep the original thinking animation for other elements */
#thinkingAnimation {
    height: 40px;
}

#thinkingAnimation span {
    display: block;
    width: 10px;
    height: 10px;
    margin: 0 5px;
    background-color: #fff;
    border-radius: 50%;
    opacity: 0.8;
    animation: bounce 1.2s infinite ease-in-out;
}

#thinkingAnimation span:nth-child(1) {
    animation-delay: -0.32s;
}

#thinkingAnimation span:nth-child(2) {
    animation-delay: -0.16s;
}

#thinkingAnimation span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.language-switch {
    position: fixed;
    bottom: 70px;
    left: 20px;
    display: none;
    flex-direction: column;
    gap: 10px;
    z-index: 9999;
}

.language-button {
    cursor: pointer;
    padding: 5px 10px;
    border: 1px solid #fff;
    border-radius: 5px;
    background-color: transparent;
    color: #fff;
}

.language-button.active {
    background-color: #dc3545;
}

#langToggleButton {
    position: fixed;
    bottom: 20px;
    left: 20px;
    cursor: pointer;
    padding: 5px 10px;
    border: 1px solid #fff;
    border-radius: 5px;
    background-color: transparent;
    color: #fff;
}

.nav-tabs {
    border: 0px;
}

.nav-link {
    background: rgb(201 201 201 / 20%);
    color: #aaaaaa;
    margin-top: 2px;
    height: 39px;
    min-width: 110px;
}

.nav-tabs:nth-child(1) .nav-link {
    border-top-left-radius: 1.2rem;
    border-bottom-left-radius: 1.2rem;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
.nav-tabs .nav-item:nth-child(2) .nav-link {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: 1.2rem;
    border-bottom-right-radius: 1.2rem;
}

.nav-link:hover {
    border: 2px;
    color: #eee;
}

.summary-container {
    display: flex;
    flex-direction: column;
    width: 90%;
    max-width: 1200px;
    margin: 116px auto 0;
    height: calc(100vh - 136px);
    padding: 20px;
}

.summary-controls {
    margin-bottom: 20px;
}

#streamedSummaryText {
    flex-grow: 1;
    overflow: auto;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    font-size: 16px;
    color: #fff;
}

#btnGenerateSummary {
    position: relative;
    z-index: 2;
    box-shadow: 0px 0px 35px rgb(34 122 180 / 60%);
}

#btnReGenerateSummary {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: fixed;
    bottom: 20px;
    box-shadow: 0px 0px 35px rgb(34 122 180 / 65%);
    z-index: 99;
}

.recognition-error {
    position: relative;
    z-index: 1000;
    margin: 10px auto;
    width: 80%;
    text-align: center;
    font-weight: bold;
    background-color: rgba(220, 53, 69, 0.8);
    border: none;
    color: white;
}

/* Toggle Header Button */
.toggle-header-btn {
    position: fixed;
    top: 88px;
    right: 20px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1200;
    transition: all 0.3s ease;
    padding: 0;
}

.toggle-header-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.toggle-header-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.toggle-header-btn.collapsed i {
    transform: rotate(180deg);
}

/* Header collapsed state */
.headers-collapsed #overAllHeader {
    transform: translateY(-100%);
}

.headers-collapsed #menuTabHeader {
    transform: translateY(-100%);
    display: none !important;
}

.headers-collapsed .split-screen-container {
    height: calc(100vh - 20px);
    margin-top: 20px;
}

.headers-collapsed .summary-container {
    margin-top: 20px;
    height: calc(100vh - 40px);
}

/* Transitions for header elements */
#overAllHeader, #menuTabHeader {
    transition: transform 0.3s ease;
}

#streamedSummaryText, .summary-container {
    transition: margin-top 0.3s ease, height 0.3s ease;
}

/* Settings Button */
.settings-button {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1200;
    transition: all 0.3s ease;
    padding: 0;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
}

/* Adjust settings button position when headers are collapsed */
.headers-collapsed .settings-button {
    top: calc(50% - 30px);
}

.settings-button:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.4);
}

.settings-button i {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.settings-button:hover i {
    transform: rotate(90deg);
}

/* Settings Modal */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1300;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.settings-modal-content {
    background-color: #2a2a2a;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header h3 {
    margin: 0;
    color: #fff;
    font-size: 1.5rem;
}

.settings-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

.settings-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.settings-body {
    padding: 25px;
}

.settings-section {
    margin-bottom: 30px;
}

.settings-section h4 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 8px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    color: #fff;
    margin-bottom: 8px;
    font-weight: 500;
}

.setting-group .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    border-radius: 6px;
    padding: 10px;
}

.setting-group .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: #f06423;
    box-shadow: 0 0 0 0.2rem rgba(240, 100, 35, 0.25);
    color: #fff;
}

.setting-group .form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.setting-group textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.form-range {
    background: transparent;
}

.form-range::-webkit-slider-track {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
}

.form-range::-webkit-slider-thumb {
    background: #f06423;
    border: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
}

.form-range::-moz-range-track {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
    border: none;
}

.form-range::-moz-range-thumb {
    background: #f06423;
    border: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
}

/* Theme Options */
.theme-options {
    display: flex;
    gap: 20px;
}

.theme-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.05);
}

.theme-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.theme-option input[type="radio"] {
    margin-right: 8px;
    accent-color: #f06423;
}

.theme-option input[type="radio"]:checked + .theme-label {
    color: #f06423;
    font-weight: 600;
}

.theme-label {
    color: #fff;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.settings-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.settings-footer .btn {
    flex: 1;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.settings-footer .btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.settings-footer .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.settings-footer .btn-primary {
    background-color: #f06423;
    border: 1px solid #f06423;
    color: #fff;
}

.settings-footer .btn-primary:hover {
    background-color: #e55a1f;
    border-color: #e55a1f;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(240, 100, 35, 0.3);
}

/* Light Mode Theme */
body.light-mode {
    background-color: #f8f9fa;
    color: #212529;
}

body.light-mode #overAllHeader {
    background-color: #ffffff;
    color: #212529;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body.light-mode .nav-link {
    background: rgba(0, 0, 0, 0.05);
    color: #6c757d;
}

body.light-mode .nav-link.active {
    background: #f06423;
    color: #fff;
}

body.light-mode .nav-link:hover {
    color: #495057;
}

body.light-mode .content-header {
    background-color: rgba(0, 0, 0, 0.05);
    color: #212529;
}

body.light-mode .action-btn {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057;
}

body.light-mode .action-btn:hover {
    background-color: #f06423;
    color: #fff;
    border-color: #e55a1f;
}

body.light-mode .collapse-expand-btn {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057;
}

body.light-mode .collapse-expand-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

body.light-mode .collapse-expand-btn.is-collapsed {
    background-color: rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 0, 0.2);
}

body.light-mode #outputText {
    background-color: rgba(0, 0, 0, 0.02);
    color: #212529;
}

body.light-mode #outputText .final {
    color: #212529;
}

body.light-mode #outputText .interim {
    color: #6f42c1;
}

body.light-mode #autoSummaryText {
    background-color: rgba(0, 0, 0, 0.02);
    color: #212529;
}

body.light-mode #streamedSummaryText {
    background-color: rgba(0, 0, 0, 0.02);
    color: #212529;
}

body.light-mode .auto-summary-header {
    color: #212529;
}

body.light-mode .split-screen-left {
    border-right: 1px solid #dee2e6;
}

body.light-mode .settings-button {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057;
}

body.light-mode .settings-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #212529;
}

body.light-mode .settings-modal-content {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
}

body.light-mode .settings-header {
    border-bottom: 1px solid #dee2e6;
}

body.light-mode .settings-header h3 {
    color: #212529;
}

body.light-mode .settings-close {
    color: #495057;
}

body.light-mode .settings-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body.light-mode .settings-section h4 {
    color: #212529;
    border-bottom: 1px solid #dee2e6;
}

body.light-mode .setting-group label {
    color: #495057;
}

body.light-mode .setting-group .form-control {
    background-color: #ffffff;
    border: 1px solid #ced4da;
    color: #495057;
}

body.light-mode .setting-group .form-control:focus {
    background-color: #ffffff;
    border-color: #f06423;
    box-shadow: 0 0 0 0.2rem rgba(240, 100, 35, 0.25);
    color: #495057;
}

body.light-mode .setting-group .form-control::placeholder {
    color: #6c757d;
}

body.light-mode .theme-option {
    border: 1px solid #ced4da;
    background-color: #ffffff;
}

body.light-mode .theme-option:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

body.light-mode .theme-label {
    color: #495057;
}

body.light-mode .settings-footer {
    border-top: 1px solid #dee2e6;
}

body.light-mode .settings-footer .btn-secondary {
    background-color: #6c757d;
    border: 1px solid #6c757d;
    color: #fff;
}

body.light-mode .settings-footer .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

body.light-mode .form-range::-webkit-slider-track {
    background: #dee2e6;
}

body.light-mode .form-range::-moz-range-track {
    background: #dee2e6;
}

body.light-mode .toggle-header-btn {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057;
}

body.light-mode .toggle-header-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

body.light-mode .language-button {
    border: 1px solid #495057;
    color: #495057;
}

body.light-mode .language-button.active {
    background-color: #dc3545;
    color: #fff;
}

body.light-mode #langToggleButton {
    border: 1px solid #495057;
    color: #495057;
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
    .settings-modal-content {
        width: 95%;
        margin: 10px;
        max-height: 95vh;
    }

    .settings-header {
        padding: 15px 20px;
    }

    .settings-body {
        padding: 20px;
    }

    .settings-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .settings-footer .btn {
        margin-bottom: 10px;
    }

    .settings-footer .btn:last-child {
        margin-bottom: 0;
    }

    .theme-options {
        flex-direction: column;
        gap: 10px;
    }

    .settings-button {
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .settings-button i {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .settings-modal-content {
        width: 98%;
        margin: 5px;
    }

    .settings-header h3 {
        font-size: 1.3rem;
    }

    .settings-section h4 {
        font-size: 1.1rem;
    }

    .setting-group label {
        font-size: 0.9rem;
    }

    .setting-group .form-control {
        font-size: 0.9rem;
        padding: 8px;
    }
}