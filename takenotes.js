$(document).ready(function() {
    const recordButton = $('#recordButton');
    const outputText = $('#outputText');
    const speakingAnimation = $('#speakingAnimation');
    const thinkingAnimation = $('#thinkingAnimation');
    const autoSummaryThinking = $('#autoSummaryThinking');
    const autoSummaryText = $('#autoSummaryText');
    const languageButtons = $('.language-button');
    const langToggleButton = $('#langToggleButton');
    const languageSwitch = $('#languageSwitch');
    const toggleHeaderBtn = $('#toggleHeaderBtn');
    const collapseTranscriptionBtn = $('#collapseTranscriptionBtn');
    const collapseSummaryBtn = $('#collapseSummaryBtn');
    const splitScreenContainer = $('.split-screen-container');
    var isRecording = false;
    var isRecognizing = false;
    var isProcessing = false; // To track if we are processing the response
    var isAutoSummarizing = false; // To track if auto-summarization is in progress
    var isTranscriptionCollapsed = false; // To track if transcription area is collapsed
    var isSummaryCollapsed = false; // To track if summary area is collapsed
    var isManualStop = false; // To track if the stop action was manual
    var stopTimeout; // Variable to store the timeout ID
    var autoSummaryTimer; // Timer for auto-summarization
    var lastSummaryTime = 0; // Track when the last summary was generated
    var recognitionLang = userSelectedLang; // Default language
    var autoSummaryEnabled = true; // Toggle for auto-summarization feature
    var headersCollapsed = false; // Track if headers are collapsed
    var autoSummaryRetryCount = 0; // Counter for auto-summary retry attempts
    var autoSummaryMaxRetries = 30; // Maximum number of retry attempts
    var autoSummaryTimeout; // Timeout for auto-summary generation
    var currentAutoSummaryEventSource = null; // Track the current auto summary event source

    // Settings variables
    var appSettings = {
        theme: 'dark',
        model: 'gpt-4o',
        temperature: 0.1,
        background: 'this is a transcript from a RGC forum. the forum will include different speakers. The forum will mainly use English.',
        instructions: `
- Below is an existing summary followed by new transcript content.
- Create a (consolidated and coherent) summary that includes both the existing information and any new key points from the transcript in chronological order.
- Avoid repetition and redundancy.
- have to group the points by category and speaker.
- output to use the same language as the input
- you should keep the content of existing summary unless the new transcript content contradicts it or have to fix the existing summary to make sense, in that case you should fix the existing summary, or you may have to group the new points into previous categories or a new category, and append the new key points to the existing summary by using bullet points
- but the new transcript content maybe just a small part(lastest part) of the whole conversation, so you should not remove any existing summary that is not contradicted or mentioned by the new transcript content
- in the last Q&A section (Q&A section will be followed by the sharing of all speakers), try to group the questions and answers together, e.g. Q:<question>\n A:<answer>
- all the points in the summary output should display in chronological order base on the transcript
`
    };

    function randomWaveBar() {
        // Add random animation delays to each wave bar
        $('#speakingAnimation .synthesis-bar').each(function() {
            var randomDelay = (Math.random() * 0.6).toFixed(2) + 's';
            $(this).css('animation-delay', randomDelay);
        });
        $('#speakingAnimation .synthesis-bar').addClass('animate');
    }
    randomWaveBar();

    // Initialize speech configuration first
    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription("3MGmZZp9TLDuXtcBFE0T5JpvngRLvXef3BzBMs75ymO3GhMa5LDmJQQJ99BEACYeBjFXJ3w3AAAYACOGsR11", "eastus");
    speechConfig.speechRecognitionLanguage = recognitionLang;
    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
    // Use let instead of const so we can reassign it later
    let recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

    languageButtons.on('click', function() {
        languageButtons.removeClass('active');
        $(this).addClass('active');
        recognitionLang = $(this).data('lang');
        langToggleButton.text($(this).text());

        // Update the speech config with the new language
        speechConfig.speechRecognitionLanguage = recognitionLang;
    });

    langToggleButton.on('click', function() {
        languageSwitch.toggle('slide', { direction: 'down' }, 200);
    });

    //languageButtons.first().trigger('click');
    languageSwitch.find('.language-button[data-lang="' + recognitionLang + '"]').trigger('click');

    $(document).on('click', function(event) {
        if (!$(event.target).closest('#languageSwitch, #langToggleButton').length) {
            if (languageSwitch.is(':visible')) {
                languageSwitch.hide('slide', { direction: 'down' }, 200);
            }
        }
    });

    recordButton.on('click', function() {
        $('#streamedSummaryText').empty(); // Clear previous results
        if (isRecording) {
            isManualStop = true;
            // console.log('Stop recording');
            stopRecognition();
        } else {
            // console.log('Start recording');
            isManualStop = false;
            startRecognition();
        }
    });

    // Function to generate auto-summary
    function generateAutoSummary() {
        // Reset retry counter when starting a new summary generation
        autoSummaryRetryCount = 0;

        // Clear any existing timeout
        if (autoSummaryTimeout) {
            clearTimeout(autoSummaryTimeout);
        }

        // Call the actual implementation
        tryGenerateAutoSummary();
    }

    // Implementation function that handles retries
    function tryGenerateAutoSummary() {
        // Check if recording is still active
        if (!isRecording) {
            console.log('Recording stopped, canceling auto summary generation');
            isAutoSummarizing = false;
            autoSummaryThinking.hide();
            return; // Don't generate summary if recording has stopped
        }

        const inputText = outputText.text().trim();
        if (inputText.length === 0) {
            return; // No text to summarize
        }

        // Close any existing event source before creating a new one
        if (currentAutoSummaryEventSource) {
            try {
                currentAutoSummaryEventSource.close();
                currentAutoSummaryEventSource = null;
            } catch (e) {
                console.error('Error closing previous event source:', e);
            }
        }

        isAutoSummarizing = true;
        autoSummaryThinking.show();

        // Get existing summary if available
        const existingSummary = autoSummaryText.text().trim();

        // Set a timeout to detect if the summary generation is taking too long
        if (autoSummaryTimeout) {
            clearTimeout(autoSummaryTimeout);
        }

        autoSummaryTimeout = setTimeout(function() {
            // If we're still summarizing after 20 seconds, it's likely stuck
            if (isAutoSummarizing && isRecording) {
                console.log('Auto-summary generation timeout, attempting retry...');
                retryAutoSummary();
            } else if (!isRecording) {
                // If recording has stopped, cancel the auto summary
                cleanupAutoSummary();
            }
        }, 20000); // 20 seconds timeout

        // Use POST instead of GET to avoid URL length limitations
        // First create a form data object
        const formData = new FormData();
        formData.append('inputText', inputText);
        formData.append('existingSummary', existingSummary);
        formData.append('model', appSettings.model);
        formData.append('temperature', appSettings.temperature);
        formData.append('background', appSettings.background);
        formData.append('instructions', appSettings.instructions);

        // Make a POST request to get a session ID
        fetch('./streamAutoSummary.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(sessionId => {
            // Check again if recording is still active
            if (!isRecording) {
                console.log('Recording stopped after session creation, canceling auto summary');
                return; // Don't proceed if recording has stopped
            }

            // Track if we've received any content
            let hasReceivedContent = false;
            // Create a temporary container to collect all streamed content
            let tempSummaryContent = '';

            // Now create EventSource with the session ID
            const eventSource = new EventSource(`./streamAutoSummary.php?session=${sessionId}`);
            currentAutoSummaryEventSource = eventSource; // Store reference to current event source

            eventSource.onmessage = function(event) {
                // Check if recording is still active
                if (!isRecording) {
                    console.log('Recording stopped during event streaming, closing event source');
                    eventSource.close();
                    cleanupAutoSummary();
                    return;
                }

                // Clear the timeout since we're receiving data
                if (autoSummaryTimeout) {
                    clearTimeout(autoSummaryTimeout);
                }

                hasReceivedContent = true;
                const data = JSON.parse(event.data);
                let thisContent = data.content;

                // Append to our temporary container instead of directly to the DOM
                tempSummaryContent += thisContent;

                // Reset the timeout for continued streaming
                autoSummaryTimeout = setTimeout(function() {
                    if (isAutoSummarizing && isRecording) {
                        console.log('Auto-summary streaming timeout, attempting to close and retry...');
                        eventSource.close();
                        currentAutoSummaryEventSource = null;
                        retryAutoSummary();
                    } else if (!isRecording) {
                        // If recording has stopped, cancel the auto summary
                        eventSource.close();
                        currentAutoSummaryEventSource = null;
                        cleanupAutoSummary();
                    }
                }, 10000); // 10 seconds timeout for continued streaming
            };

            eventSource.addEventListener('close', function() {
                // Clear the timeout since we're done
                if (autoSummaryTimeout) {
                    clearTimeout(autoSummaryTimeout);
                }

                // Check if recording is still active
                if (!isRecording) {
                    console.log('Recording stopped during event close, cleaning up');
                    cleanupAutoSummary();
                    return;
                }

                // Only format if we received content
                if (hasReceivedContent && tempSummaryContent.trim() !== '') {
                    // Clear the previous content
                    autoSummaryText.empty();

                    // Apply all the content at once
                    autoSummaryText.text(tempSummaryContent);

                    // Format the summary with markdown
                    autoSummaryText.html(marked.parse(escapeLatex(autoSummaryText.text())));

                    // Scroll to the bottom after applying markdown
                    scrollToBottom(autoSummaryText);
                } else if (isRecording) {
                    // If we didn't receive any content but recording is still active, retry
                    retryAutoSummary();
                    return;
                }

                // Clean up the event source
                eventSource.close();
                currentAutoSummaryEventSource = null;
                autoSummaryThinking.hide();
                isAutoSummarizing = false;
                // Reset retry counter on successful completion
                autoSummaryRetryCount = 0;
            });

            eventSource.onerror = function() {
                // Clear the timeout since we got an error
                if (autoSummaryTimeout) {
                    clearTimeout(autoSummaryTimeout);
                }

                // Check if recording is still active
                if (!isRecording) {
                    console.log('Recording stopped during event error, cleaning up');
                    eventSource.close();
                    currentAutoSummaryEventSource = null;
                    cleanupAutoSummary();
                    return;
                }

                // Close the event source
                eventSource.close();
                currentAutoSummaryEventSource = null;

                // If we haven't received any content and recording is still active, retry
                if ((!hasReceivedContent || tempSummaryContent.trim() === '') && isRecording) {
                    retryAutoSummary();
                } else if (hasReceivedContent) {
                    // If we did receive some content but got an error, apply what we have so far
                    autoSummaryText.empty();
                    autoSummaryText.text(tempSummaryContent);

                    // Format the summary with markdown
                    autoSummaryText.html(marked.parse(escapeLatex(autoSummaryText.text())));

                    // Scroll to the bottom after applying markdown
                    scrollToBottom(autoSummaryText);

                    autoSummaryThinking.hide();
                    isAutoSummarizing = false;
                }
            };
        })
        .catch(error => {
            // Clear the timeout since we got an error
            if (autoSummaryTimeout) {
                clearTimeout(autoSummaryTimeout);
            }

            console.error('Error setting up auto-summary:', error);
            retryAutoSummary();
        });
    }

    // Function to handle retry logic
    function retryAutoSummary() {
        // Check if recording is still active
        if (!isRecording) {
            console.log('Recording stopped, canceling auto summary retry');
            cleanupAutoSummary();
            return; // Don't retry if recording has stopped
        }

        autoSummaryRetryCount++;
        console.log(`Auto-summary retry attempt ${autoSummaryRetryCount} of ${autoSummaryMaxRetries}`);

        if (autoSummaryRetryCount <= autoSummaryMaxRetries) {
            // Hide the thinking animation briefly to indicate a retry
            autoSummaryThinking.hide();

            // Calculate delay with exponential backoff (1s, 2s, 4s)
            const retryDelay = Math.min(1000 * Math.pow(2, autoSummaryRetryCount - 1), 5000);
            console.log(`Waiting ${retryDelay}ms before retry attempt ${autoSummaryRetryCount}`);

            setTimeout(function() {
                // Check again if recording is still active before showing animation
                if (!isRecording) {
                    console.log('Recording stopped during retry delay, canceling retry');
                    cleanupAutoSummary();
                    return;
                }

                autoSummaryThinking.show();
                // Try again
                tryGenerateAutoSummary();
            }, retryDelay);
        } else {
            // Max retries reached, give up
            console.error('Max auto-summary retry attempts reached');
            autoSummaryThinking.hide();
            isAutoSummarizing = false;
            autoSummaryText.html('<div class="alert alert-danger">Failed to generate summary after multiple attempts.</div>');
            // Reset retry counter
            autoSummaryRetryCount = 0;
        }
    }

    // Helper function to clean up auto summary resources
    function cleanupAutoSummary() {
        console.log('Cleaning up auto summary resources');

        // Clear any pending timeouts
        if (autoSummaryTimeout) {
            clearTimeout(autoSummaryTimeout);
            autoSummaryTimeout = null;
        }

        // Close any existing event source
        if (currentAutoSummaryEventSource) {
            try {
                currentAutoSummaryEventSource.close();
                currentAutoSummaryEventSource = null;
            } catch (e) {
                console.error('Error closing event source during cleanup:', e);
            }
        }

        // Reset flags and UI
        isAutoSummarizing = false;
        autoSummaryThinking.hide();
        autoSummaryRetryCount = 0;
    }

    function startRecognition() {
        // console.log('Start Recognition');
        if (!isManualStop) {
            if (!isRecognizing && !isProcessing) {
                try {
                    // First check if microphone is available
                    checkMicrophoneAccess()
                        .then(available => {
                            if (available) {
                                isRecording = true;
                                isRecognizing = true;
                                $('#recordButton').addClass('recording');

                                // Dispose of the old recognizer if it exists
                                if (recognizer) {
                                    try {
                                        recognizer.close();
                                    } catch (e) {
                                        console.log('Error closing previous recognizer:', e);
                                    }
                                }

                                // Create a new recognizer with the existing config
                                // This avoids context closed issues while reusing the configuration
                                // Use a new audio config each time
                                let newAudioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
                                recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, newAudioConfig);

                                // Set up event handlers for the new recognizer
                                setupRecognizerEvents();

                                // Start recognition
                                recognizer.startContinuousRecognitionAsync(
                                    function() {
                                        // Success callback
                                        console.log('Recognition started successfully');
                                    },
                                    function(error) {
                                        // Error callback
                                        console.error('Error starting recognition:', error);
                                        handleRecognitionError('Failed to start speech recognition. Please try again.');
                                    }
                                );
                            } else {
                                handleRecognitionError('Microphone access is not available. Please check your browser permissions.');
                            }
                        })
                        .catch(error => {
                            console.error('Error checking microphone:', error);
                            handleRecognitionError('Could not check microphone access. Please ensure your browser supports microphone access.');
                        });
                } catch (error) {
                    console.error('Error in recognition setup:', error);
                    handleRecognitionError('An error occurred while setting up speech recognition.');
                }
            } else {
                // console.log('Recognition already started or processing');
            }
        }
    }

    // Function to check if microphone is available
    async function checkMicrophoneAccess() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            // Clean up the stream immediately after checking
            stream.getTracks().forEach(track => track.stop());
            return true;
        } catch (error) {
            console.error('Microphone access error:', error);
            return false;
        }
    }

    // Function to handle recognition errors
    function handleRecognitionError(message) {
        isRecording = false;
        isRecognizing = false;
        $('#recordButton').removeClass('recording');

        // Show error message to user
        const errorHtml = `<div class="alert alert-danger recognition-error">${message}</div>`;
        outputText.prepend(errorHtml);

        // Remove error message after 5 seconds
        setTimeout(() => {
            $('.recognition-error').fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    function stopRecognition() {
        console.log('Stop Recognition');

        // Set recording flag to false immediately to prevent new auto summaries
        isRecording = false;

        if (isRecognizing) {
            isRecognizing = false;
            recordButton.removeClass('recording');

            // Clean up any ongoing auto summary
            if (isAutoSummarizing) {
                console.log('Cleaning up auto summary during stop recognition');
                cleanupAutoSummary();
            }

            try {
                recognizer.stopContinuousRecognitionAsync(
                    function() {
                        // Success callback
                        console.log('Recognition stopped successfully');
                    },
                    function(error) {
                        // Error callback
                        console.error('Error stopping recognition:', error);
                    }
                );
            } catch (error) {
                console.error('Error in stopRecognition:', error);
            }
        } else {
            console.log('Recognition already stopped');

            // Still clean up any ongoing auto summary even if recognition was already stopped
            if (isAutoSummarizing) {
                console.log('Cleaning up auto summary even though recognition was already stopped');
                cleanupAutoSummary();
            }
        }
    }

    // Function to set up event handlers for the recognizer
    function setupRecognizerEvents() {
        recognizer.recognizing = function (_, e) {
            // console.log('Recognizing:', e.result.text);
            outputText.find('.interim').remove();
            outputText.append("<span class='interim'>" + e.result.text + "</span>");
            scrollToBottom(outputText);
        };

        recognizer.recognized = function (_, e) {
            if (e.result.text !== undefined && e.result.text !== "") {
                // console.log('Recognized:', e.result.text);
                outputText.find('.interim').remove();
                outputText.append("<div class='final'>" + e.result.text + "</div>");
                // Make sure to scroll to the bottom to show the latest content
                scrollToBottom(outputText);

                if (autoSummaryEnabled) {
                    if (isRecording && !isAutoSummarizing) {
                        const currentTime = Date.now();
                        // Check if 10 seconds have passed since the last summary
                        if (currentTime - lastSummaryTime >= 10000) { // Changed to 10 seconds (10000ms)
                            generateAutoSummary();
                            lastSummaryTime = currentTime;
                        }
                    }
                }
            }
            if (!isRecognizing && isRecording) {
                // Only restart if we're supposed to be recording but recognition stopped
                startRecognition();
            }
        };

        recognizer.canceled = function (_, e) {
            console.log('Recognition canceled, reason:', e.reason);
            if (e.reason === SpeechSDK.CancellationReason.Error) {
                console.error('Recognition error:', e.errorDetails);
                handleRecognitionError(`Recognition error: ${e.errorDetails}`);
            }
            stopRecognition();
        };

        recognizer.sessionStarted = function () {
            console.log('Session started');
        };

        recognizer.sessionStopped = function () {
            console.log('Session stopped');
            isRecognizing = false;
            recordButton.removeClass('recording');
            if (!isProcessing && isRecording) {
                // Only restart if we're supposed to be recording
                startRecognition();
            }
        };
    }

    // Set up initial event handlers
    setupRecognizerEvents();


    //markdown
    marked.setOptions({
        highlight: function (code, language) {
            const validLanguage = hljs.getLanguage(language) ? language : 'javascript';
            return hljs.highlight(code, { language: validLanguage }).value;
        },
    });

    function escapeLatex(inputValue) {
        const replacements = [
            { regex: /\\\[/g, replacement: '\\\\[' },
            { regex: /\\\]/g, replacement: '\\\\]' },
            { regex: /\\\(/g, replacement: '\\\\(' },
            { regex: /\\\)/g, replacement: '\\\\)' }
        ];

        return replacements.reduce((acc, { regex, replacement }) => {
            return acc.replace(regex, replacement);
        }, inputValue);
    }

    $('#btnGenerateSummary, #btnReGenerateSummary').on('click', function() {
        $(this).hide();
        const inputText = outputText.text().trim();
        if (inputText.length === 0) {
            alert('No transcript to summarize');
            $(this).show();
            return;
        }

        $('#streamedSummaryText').empty(); // Clear previous results
        thinkingAnimation.show();

        // Use POST instead of GET to avoid URL length limitations
        // First create a form data object
        const formData = new FormData();
        formData.append('inputText', inputText);

        // Make a POST request to get a session ID
        fetch('./streamOpenAIResponse.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(sessionId => {
            // Now create EventSource with the session ID
            const eventSource = new EventSource(`./streamOpenAIResponse.php?session=${sessionId}`);

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                let thisContent = data.content;
                $('#streamedSummaryText').append(thisContent);
                // Auto-scroll to the bottom to show the latest content
                scrollToBottom(document.getElementById('streamedSummaryText'));
            };

            eventSource.addEventListener('close', function() {
                // console.log('Stream ended.');
                $('#streamedSummaryText').html(marked.parse(escapeLatex($('#streamedSummaryText').text())));
                // Scroll to the bottom after applying markdown
                scrollToBottom(document.getElementById('streamedSummaryText'));
                eventSource.close();
                thinkingAnimation.hide();
                $('#btnReGenerateSummary').show();
            });

            eventSource.onerror = function() {
                eventSource.close();
                thinkingAnimation.hide();
                $('#btnReGenerateSummary').show();
                $('#streamedSummaryText').html('<div class="alert alert-danger">Error generating summary. Please try again.</div>');
            };
        })
        .catch(error => {
            console.error('Error setting up summary:', error);
            thinkingAnimation.hide();
            $('#btnReGenerateSummary').show();
            $('#streamedSummaryText').html('<div class="alert alert-danger">Error generating summary. Please try again.</div>');
        });
    });

    $('#tasks-tab').on('click', function() {
        $('#btnGenerateSummary, #btnReGenerateSummary').hide();
        $('#recordButton').show();
    });

    $('#summary-tab').on('click', function() {
        if ($('#streamedSummaryText').html() === '') {
            $('#btnGenerateSummary').show();
        } else {
            $('#btnReGenerateSummary').show();
        }
        isManualStop = true;
        // console.log('Stop recording');
        stopRecognition();
        clearInterval(autoSummaryTimer); // Clear the auto-summary timer
        $('#recordButton').hide();
    });

    // Copy to clipboard functionality
    function copyToClipboard(text, successMessage = 'Copied to clipboard!') {
        if (navigator.clipboard) {
            // Modern approach using Clipboard API
            navigator.clipboard.writeText(text)
                .then(() => {
                    showToast(successMessage);
                })
                .catch(err => {
                    console.error('Could not copy text: ', err);
                    showToast('Failed to copy to clipboard', 'error');
                    fallbackCopyToClipboard(text, successMessage);
                });
        } else {
            // Fallback for browsers without clipboard API support
            fallbackCopyToClipboard(text, successMessage);
        }
    }

    // Fallback method for copying to clipboard
    function fallbackCopyToClipboard(text, successMessage) {
        try {
            const textarea = document.createElement('textarea');
            textarea.value = text;

            // Make the textarea out of viewport
            textarea.style.position = 'fixed';
            textarea.style.left = '-999999px';
            textarea.style.top = '-999999px';

            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();

            // Use the deprecated execCommand as a last resort
            const successful = document.execCommand('copy');

            if (successful) {
                showToast(successMessage);
            } else {
                showToast('Failed to copy to clipboard', 'error');
            }

            document.body.removeChild(textarea);
        } catch (err) {
            console.error('Fallback clipboard copy failed: ', err);
            showToast('Failed to copy to clipboard', 'error');
        }
    }

    // Helper function to scroll an element to the bottom
    function scrollToBottom(element) {
        if (element instanceof jQuery) {
            if (element.length > 0) {
                element.get(0).scrollTop = element.get(0).scrollHeight;
            }
        } else if (element instanceof HTMLElement) {
            element.scrollTop = element.scrollHeight;
        }
    }

    // Show toast notification
    function showToast(message, type = 'success') {
        // Create toast container if it doesn't exist
        if (!$('#toastContainer').length) {
            $('body').append('<div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>');
        }

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : 'bg-danger';

        const toast = `
            <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // Add toast to container
        $('#toastContainer').append(toast);

        // Show toast
        $(`#${toastId}`).toast({
            delay: 3000,
            autohide: true
        }).toast('show');

        // Remove toast after it's hidden
        $(`#${toastId}`).on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // Export to text functionality
    function exportToText(content, title, type) {
        showToast('Preparing document for export...', 'success');

        // Create a form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = './exportToText.php'; // Use text export instead of Word
        form.style.display = 'none';

        // Add content field
        const contentField = document.createElement('input');
        contentField.type = 'hidden';
        contentField.name = 'content';
        contentField.value = content;
        form.appendChild(contentField);

        // Add title field
        const titleField = document.createElement('input');
        titleField.type = 'hidden';
        titleField.name = 'title';
        titleField.value = title;
        form.appendChild(titleField);

        // Add type field
        const typeField = document.createElement('input');
        typeField.type = 'hidden';
        typeField.name = 'type';
        typeField.value = type;
        form.appendChild(typeField);

        // Create an iframe to handle the response
        const iframe = document.createElement('iframe');
        iframe.name = 'export-frame';
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        // Set the form target to the iframe
        form.target = 'export-frame';

        // Add event listeners to the iframe
        iframe.addEventListener('load', function() {
            try {
                // Try to access the iframe content
                const iframeContent = iframe.contentDocument || iframe.contentWindow.document;

                // Check if there's an error message (JSON response)
                if (iframeContent.body.textContent.trim().startsWith('{')) {
                    try {
                        const errorData = JSON.parse(iframeContent.body.textContent);
                        if (errorData.error) {
                            showToast('Export failed: ' + errorData.error, 'error');
                        }
                    } catch (e) {
                        // If it's not valid JSON but still not a download, show generic error
                        showToast('Export failed. Please try again.', 'error');
                    }
                } else {
                    // If we can access the content but it's not an error, it's likely a successful download
                    showToast('Text file exported successfully!', 'success');
                }
            } catch (e) {
                // If we can't access the iframe content, it's likely due to a successful download
                // (cross-origin restriction when downloading)
                showToast('Text file exported successfully!', 'success');
            }

            // Clean up
            setTimeout(() => {
                document.body.removeChild(form);
                document.body.removeChild(iframe);
            }, 1000);
        });

        iframe.addEventListener('error', function() {
            showToast('Export failed. Please try again.', 'error');
            // Clean up
            setTimeout(() => {
                document.body.removeChild(form);
                document.body.removeChild(iframe);
            }, 1000);
        });

        // Add form to document and submit
        document.body.appendChild(form);
        form.submit();
    }

    // Copy transcript button
    $('#copyTranscriptBtn').on('click', function() {
        const text = outputText.text().trim();
        if (text) {
            copyToClipboard(text, 'Transcript copied to clipboard!');
        } else {
            showToast('No transcript to copy', 'error');
        }
    });

    // Export transcript button
    $('#exportTranscriptBtn').on('click', function() {
        const text = outputText.text().trim();
        if (text) {
            exportToText(text, 'Transcript', 'transcript');
        } else {
            showToast('No transcript to export', 'error');
        }
    });

    // Copy auto summary button
    $('#copySummaryBtn').on('click', function() {
        const text = autoSummaryText.text().trim();
        if (text) {
            copyToClipboard(text, 'Auto summary copied to clipboard!');
        } else {
            showToast('No auto summary to copy', 'error');
        }
    });

    // Export auto summary button
    $('#exportSummaryBtn').on('click', function() {
        const text = autoSummaryText.text().trim();
        if (text) {
            exportToText(text, 'Auto Summary', 'summary');
        } else {
            showToast('No auto summary to export', 'error');
        }
    });

    // Copy full summary button
    $('#copyFullSummaryBtn').on('click', function() {
        const text = $('#streamedSummaryText').text().trim();
        if (text) {
            copyToClipboard(text, 'Full summary copied to clipboard!');
        } else {
            showToast('No full summary to copy', 'error');
        }
    });

    // Export full summary button
    $('#exportFullSummaryBtn').on('click', function() {
        const text = $('#streamedSummaryText').text().trim();
        if (text) {
            exportToText(text, 'Full Summary', 'summary');
        } else {
            showToast('No full summary to export', 'error');
        }
    });

    // Font size adjustment functionality

    // Store default font sizes to allow resetting
    const defaultFontSizes = {
        outputText: parseFloat(getComputedStyle(document.getElementById('outputText')).fontSize),
        autoSummaryText: parseFloat(getComputedStyle(document.getElementById('autoSummaryText')).fontSize),
        streamedSummaryText: 16 // Default size for streamed summary
    };

    // Store current font sizes
    let currentFontSizes = {
        outputText: defaultFontSizes.outputText,
        autoSummaryText: defaultFontSizes.autoSummaryText,
        streamedSummaryText: defaultFontSizes.streamedSummaryText
    };

    // Font size adjustment step (in pixels)
    const fontSizeStep = 2;

    // Min and max font sizes
    const minFontSize = 12;
    const maxFontSize = 50;

    // Function to update font size
    function updateFontSize(elementId, increment) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // Get current font size
        let currentSize = currentFontSizes[elementId];

        // Calculate new size
        let newSize = increment ?
            Math.min(currentSize + fontSizeStep, maxFontSize) :
            Math.max(currentSize - fontSizeStep, minFontSize);

        // Update the element's font size
        element.style.fontSize = newSize + 'px';

        // Update stored current size
        currentFontSizes[elementId] = newSize;

        // Show feedback to user (commented out to avoid distracting notifications)
        // const action = increment ? 'increased' : 'decreased';
        // showToast(`Font size ${action} to ${newSize}px`, 'success');
    }

    // Transcript font size controls
    $('#increaseTranscriptFont').on('click', function() {
        updateFontSize('outputText', true);
    });

    $('#decreaseTranscriptFont').on('click', function() {
        updateFontSize('outputText', false);
    });

    // Auto summary font size controls
    $('#increaseSummaryFont').on('click', function() {
        updateFontSize('autoSummaryText', true);
    });

    $('#decreaseSummaryFont').on('click', function() {
        updateFontSize('autoSummaryText', false);
    });

    // Full summary font size controls
    $('#increaseFullSummaryFont').on('click', function() {
        updateFontSize('streamedSummaryText', true);
    });

    $('#decreaseFullSummaryFont').on('click', function() {
        updateFontSize('streamedSummaryText', false);
    });

    // Toggle header visibility
    $('#toggleHeaderBtn').on('click', function() {
        headersCollapsed = !headersCollapsed;

        if (headersCollapsed) {
            $('body').addClass('headers-collapsed');
            $(this).addClass('collapsed');
        } else {
            $('body').removeClass('headers-collapsed');
            $(this).removeClass('collapsed');
        }

        // Save preference to localStorage
        localStorage.setItem('headersCollapsed', headersCollapsed);
    });

    // Load header visibility preference from localStorage
    function loadHeaderPreference() {
        const savedPreference = localStorage.getItem('headersCollapsed');
        if (savedPreference === 'true') {
            headersCollapsed = true;
            $('body').addClass('headers-collapsed');
            $('#toggleHeaderBtn').addClass('collapsed');
        }
    }

    // Load preferences on page load
    loadHeaderPreference();

    // Collapse/Expand Transcription area
    $('#collapseTranscriptionBtn').on('click', function() {
        isTranscriptionCollapsed = !isTranscriptionCollapsed;

        if (isTranscriptionCollapsed) {
            // Cannot collapse both areas at the same time
            if (isSummaryCollapsed) {
                isSummaryCollapsed = false;
                $('.split-screen-container').removeClass('summary-collapsed');
                $('#collapseSummaryBtn i').removeClass('bi-chevron-left').addClass('bi-chevron-right');
                $('#collapseSummaryBtn').attr('title', 'Hide Summary');
            }

            $('.split-screen-container').addClass('transcription-collapsed');
            $(this).find('i').removeClass('bi-chevron-left').addClass('bi-chevron-right');
            $(this).attr('title', 'Show Transcription');

            // Add a visual indicator that this is now an "expand" button
            $(this).addClass('is-collapsed');
        } else {
            $('.split-screen-container').removeClass('transcription-collapsed');
            $(this).find('i').removeClass('bi-chevron-right').addClass('bi-chevron-left');
            $(this).attr('title', 'Hide Transcription');

            // Remove the visual indicator
            $(this).removeClass('is-collapsed');
        }

        // Save preference to localStorage
        localStorage.setItem('isTranscriptionCollapsed', isTranscriptionCollapsed);
    });

    // Collapse/Expand Summary area
    $('#collapseSummaryBtn').on('click', function() {
        isSummaryCollapsed = !isSummaryCollapsed;

        if (isSummaryCollapsed) {
            // Cannot collapse both areas at the same time
            if (isTranscriptionCollapsed) {
                isTranscriptionCollapsed = false;
                $('.split-screen-container').removeClass('transcription-collapsed');
                $('#collapseTranscriptionBtn i').removeClass('bi-chevron-right').addClass('bi-chevron-left');
                $('#collapseTranscriptionBtn').attr('title', 'Hide Transcription');
                $('#collapseTranscriptionBtn').removeClass('is-collapsed');
            }

            $('.split-screen-container').addClass('summary-collapsed');
            $(this).find('i').removeClass('bi-chevron-right').addClass('bi-chevron-left');
            $(this).attr('title', 'Show Summary');

            // Add a visual indicator that this is now an "expand" button
            $(this).addClass('is-collapsed');
        } else {
            $('.split-screen-container').removeClass('summary-collapsed');
            $(this).find('i').removeClass('bi-chevron-left').addClass('bi-chevron-right');
            $(this).attr('title', 'Hide Summary');

            // Remove the visual indicator
            $(this).removeClass('is-collapsed');
        }

        // Save preference to localStorage
        localStorage.setItem('isSummaryCollapsed', isSummaryCollapsed);
    });

    // Load collapse/expand preferences
    function loadCollapsePreferences() {
        const savedTranscriptionCollapsed = localStorage.getItem('isTranscriptionCollapsed');
        const savedSummaryCollapsed = localStorage.getItem('isSummaryCollapsed');

        // Cannot have both collapsed at the same time, prioritize transcription
        if (savedTranscriptionCollapsed === 'true') {
            isTranscriptionCollapsed = true;
            $('.split-screen-container').addClass('transcription-collapsed');
            $('#collapseTranscriptionBtn i').removeClass('bi-chevron-left').addClass('bi-chevron-right');
            $('#collapseTranscriptionBtn').attr('title', 'Show Transcription');
            $('#collapseTranscriptionBtn').addClass('is-collapsed');
        } else if (savedSummaryCollapsed === 'true') {
            isSummaryCollapsed = true;
            $('.split-screen-container').addClass('summary-collapsed');
            $('#collapseSummaryBtn i').removeClass('bi-chevron-right').addClass('bi-chevron-left');
            $('#collapseSummaryBtn').attr('title', 'Show Summary');
            $('#collapseSummaryBtn').addClass('is-collapsed');
        }
    }

    // Load collapse/expand preferences on page load
    loadCollapsePreferences();

    // Function to restore both panels
    function restoreBothPanels() {
        // Restore both panels to their original state
        isTranscriptionCollapsed = false;
        isSummaryCollapsed = false;

        // Remove collapsed classes
        $('.split-screen-container').removeClass('transcription-collapsed summary-collapsed');

        // Reset button states
        $('#collapseTranscriptionBtn').removeClass('is-collapsed')
            .attr('title', 'Hide Transcription')
            .find('i').removeClass('bi-chevron-right').addClass('bi-chevron-left');

        $('#collapseSummaryBtn').removeClass('is-collapsed')
            .attr('title', 'Hide Summary')
            .find('i').removeClass('bi-chevron-left').addClass('bi-chevron-right');

        // Save preferences to localStorage
        localStorage.setItem('isTranscriptionCollapsed', false);
        localStorage.setItem('isSummaryCollapsed', false);
    }

    // Show both panels buttons
    $('#showBothFromTranscription, #showBothFromSummary').on('click', function() {
        restoreBothPanels();
    });

    // Settings functionality
    const settingsButton = $('#settingsButton');
    const settingsModal = $('#settingsModal');
    const closeSettings = $('#closeSettings');
    const saveSettings = $('#saveSettings');
    const resetSettings = $('#resetSettings');
    const temperatureSlider = $('#temperatureSlider');
    const temperatureValue = $('#temperatureValue');

    // Load settings from localStorage
    function loadSettings() {
        const savedSettings = localStorage.getItem('appSettings');
        if (savedSettings) {
            try {
                const parsed = JSON.parse(savedSettings);
                appSettings = { ...appSettings, ...parsed };
            } catch (e) {
                console.error('Error parsing saved settings:', e);
            }
        }
        applySettings();
    }

    // Apply settings to the UI
    function applySettings() {
        // Apply theme
        if (appSettings.theme === 'light') {
            $('body').addClass('light-mode');
            $('input[name="theme"][value="light"]').prop('checked', true);
        } else {
            $('body').removeClass('light-mode');
            $('input[name="theme"][value="dark"]').prop('checked', true);
        }

        // Apply other settings to form
        $('#modelSelect').val(appSettings.model);
        $('#temperatureSlider').val(appSettings.temperature);
        $('#temperatureValue').text(appSettings.temperature);
        $('#backgroundText').val(appSettings.background);
        $('#instructionsText').val(appSettings.instructions);
    }

    // Save settings to localStorage
    function saveSettingsToStorage() {
        localStorage.setItem('appSettings', JSON.stringify(appSettings));
        showToast('Settings saved successfully!', 'success');
    }

    // Reset settings to default
    function resetSettingsToDefault() {
        appSettings = {
            theme: 'dark',
            model: 'gpt-4o',
            temperature: 0.1,
            background: 'this is a transcript from a RGC forum. the forum will include different speakers. The forum will mainly use English.',
            instructions: `
- Below is an existing summary followed by new transcript content.
- Create a (consolidated and coherent) summary that includes both the existing information and any new key points from the transcript in chronological order.
- Avoid repetition and redundancy.
- have to group the points by category and speaker.
- Use the same language as the input
- you should keep the content of existing summary unless the new transcript content contradicts it or have to fix the existing summary to make sense, in that case you should fix the existing summary, or you may have to group the new points into previous categories or a new category, and append the new key points to the existing summary by using bullet points
- but the new transcript content maybe just a small part(lastest part) of the whole conversation, so you should not remove any existing summary that is not contradicted or mentioned by the new transcript content
- in the last Q&A section (Q&A section will be followed by the sharing of all speakers), try to group the questions and answers together, e.g. Q:<question>\n A:<answer>
- all the points in the summary output should display in chronological order base on the transcript
`
        };
        applySettings();
        showToast('Settings reset to default!', 'success');
    }

    // Event handlers
    settingsButton.on('click', function() {
        settingsModal.fadeIn(300);
    });

    closeSettings.on('click', function() {
        settingsModal.fadeOut(300);
    });

    // Close modal when clicking outside
    settingsModal.on('click', function(e) {
        if (e.target === this) {
            settingsModal.fadeOut(300);
        }
    });

    // Temperature slider
    temperatureSlider.on('input', function() {
        temperatureValue.text($(this).val());
    });

    // Save settings
    saveSettings.on('click', function() {
        // Get values from form
        appSettings.theme = $('input[name="theme"]:checked').val();
        appSettings.model = $('#modelSelect').val();
        appSettings.temperature = parseFloat($('#temperatureSlider').val());
        appSettings.background = $('#backgroundText').val();
        appSettings.instructions = $('#instructionsText').val();

        // Apply and save
        applySettings();
        saveSettingsToStorage();
        settingsModal.fadeOut(300);
    });

    // Reset settings
    resetSettings.on('click', function() {
        if (confirm('Are you sure you want to reset all settings to default?')) {
            resetSettingsToDefault();
        }
    });

    // Load settings on page load
    loadSettings();

});
